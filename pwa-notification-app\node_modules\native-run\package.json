{"name": "native-run", "version": "2.0.1", "description": "A CLI for running apps on iOS/Android devices and simulators/emulators", "bin": {"native-run": "bin/native-run"}, "scripts": {"clean": "rm -rf dist", "build": "npm run clean && tsc", "watch": "tsc -w", "test": "jest --maxWorkers=4", "lint": "npm run eslint && npm run prettier -- --check", "fmt": "npm run eslint -- --fix && npm run prettier -- --write", "prettier": "prettier \"**/*.ts\"", "eslint": "eslint . --ext .ts", "publish:ci": "semantic-release", "publish:testing": "npm version prerelease --preid=testing --no-git-tag-version && npm publish --tag=testing && git stash", "prepublishOnly": "npm run build"}, "main": "dist/index.js", "files": ["assets", "bin", "dist"], "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ionic-team/native-run.git"}, "dependencies": {"@ionic/utils-fs": "^3.1.7", "@ionic/utils-terminal": "^2.3.4", "bplist-parser": "^0.3.2", "debug": "^4.3.4", "elementtree": "^0.1.7", "ini": "^4.1.1", "plist": "^3.1.0", "split2": "^4.2.0", "through2": "^4.0.2", "tslib": "^2.6.2", "yauzl": "^2.10.0"}, "devDependencies": {"@ionic/eslint-config": "^0.3.0", "@ionic/prettier-config": "^4.0.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@types/debug": "^4.1.10", "@types/elementtree": "^0.1.3", "@types/ini": "^1.3.32", "@types/jest": "^26.0.13", "@types/node": "^16.0.0", "@types/plist": "^3.0.4", "@types/split2": "^4.2.2", "@types/through2": "^2.0.40", "@types/yauzl": "^2.10.2", "eslint": "^7.8.1", "jest": "^26.4.2", "prettier": "^3.0.3", "semantic-release": "^19.0.5", "ts-jest": "^26.3.0", "typescript": "~4.9.5"}, "prettier": "@ionic/prettier-config", "eslintConfig": {"extends": "@ionic/eslint-config/recommended", "rules": {"@typescript-eslint/explicit-module-boundary-types": ["warn", {"allowArgumentsExplicitlyTypedAsAny": true}]}}, "release": {"branches": "stable", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "keywords": ["android", "ios", "cli", "mobile", "app", "hybrid", "native"], "author": "Ionic Team <<EMAIL>> (https://ionicframework.com)", "license": "MIT", "bugs": {"url": "https://github.com/ionic-team/native-run/issues"}, "homepage": "https://github.com/ionic-team/native-run#readme"}