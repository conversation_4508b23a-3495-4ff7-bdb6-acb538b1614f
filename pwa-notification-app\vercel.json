{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "headers": [{"source": "/sw.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Service-Worker-Allowed", "value": "/"}]}, {"source": "/workbox-*.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/manifest.webmanifest", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}