<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">通知列表</h3>
    
    <div v-if="notificationStore.schedules.length === 0" class="text-center py-8">
      <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M15 17h5l-5 5v-5zM4.868 19.718A8.966 8.966 0 0112 21a8.966 8.966 0 017.132-1.282" />
      </svg>
      <p class="text-gray-500">暂无通知</p>
      <p class="text-sm text-gray-400 mt-1">添加您的第一个通知提醒</p>
    </div>

    <div v-else class="space-y-4">
      <div
        v-for="schedule in notificationStore.schedules"
        :key="schedule.id"
        class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        :class="{
          'bg-green-50 border-green-200': schedule.isActive && schedule.scheduledTime > new Date(),
          'bg-gray-50 border-gray-200': !schedule.isActive,
          'bg-red-50 border-red-200': schedule.isActive && schedule.scheduledTime <= new Date()
        }"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h4 class="font-medium text-gray-900">{{ schedule.title }}</h4>
            <p class="text-sm text-gray-600 mt-1">{{ schedule.body }}</p>
            
            <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {{ formatDateTime(schedule.scheduledTime) }}
              </span>
              
              <span v-if="schedule.repeatType !== 'none'" class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {{ getRepeatText(schedule.repeatType) }}
              </span>
              
              <span class="flex items-center">
                <div 
                  class="w-2 h-2 rounded-full mr-1"
                  :class="{
                    'bg-green-500': schedule.isActive && schedule.scheduledTime > new Date(),
                    'bg-gray-400': !schedule.isActive,
                    'bg-red-500': schedule.isActive && schedule.scheduledTime <= new Date()
                  }"
                ></div>
                {{ getStatusText(schedule) }}
              </span>
            </div>
          </div>

          <div class="flex items-center space-x-2 ml-4">
            <button
              @click="toggleActive(schedule.id)"
              class="p-1 rounded-md hover:bg-gray-100 transition-colors"
              :title="schedule.isActive ? '禁用' : '启用'"
            >
              <svg v-if="schedule.isActive" class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z" clip-rule="evenodd" />
              </svg>
            </button>

            <button
              @click="deleteNotification(schedule.id)"
              class="p-1 rounded-md hover:bg-red-100 transition-colors"
              title="删除"
            >
              <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useNotificationStore } from '../stores/notification'
import type { NotificationSchedule } from '../stores/notification'

const notificationStore = useNotificationStore()

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getRepeatText = (repeatType: string) => {
  const map = {
    'daily': '每天',
    'weekly': '每周',
    'monthly': '每月',
    'none': '不重复'
  }
  return map[repeatType as keyof typeof map] || '不重复'
}

const getStatusText = (schedule: NotificationSchedule) => {
  if (!schedule.isActive) return '已禁用'
  if (schedule.scheduledTime <= new Date()) return '已过期'
  return '活跃'
}

const toggleActive = (id: string) => {
  const schedule = notificationStore.schedules.find(s => s.id === id)
  if (schedule) {
    notificationStore.updateSchedule(id, { isActive: !schedule.isActive })
  }
}

const deleteNotification = (id: string) => {
  if (confirm('确定要删除这个通知吗？')) {
    notificationStore.deleteSchedule(id)
  }
}
</script>
