#!/bin/bash

# PWA推送通知应用部署脚本

echo "🚀 开始部署PWA推送通知应用..."

# 检查Node.js和npm
echo "📋 检查环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装，请先安装npm"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"

# 安装依赖
echo "📦 安装依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装成功"

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 项目构建失败"
    exit 1
fi

echo "✅ 项目构建成功"

# 检查构建输出
if [ ! -d "dist" ]; then
    echo "❌ 构建输出目录不存在"
    exit 1
fi

echo "✅ 构建输出检查通过"

# 预览构建结果
echo "👀 启动预览服务器..."
echo "📝 请在浏览器中访问预览地址测试应用功能"
echo "🔔 特别测试通知权限和推送功能"
echo "📱 确认PWA可以正常安装"

npm run preview

echo "🎉 部署脚本执行完成！"
echo ""
echo "📋 下一步操作："
echo "1. 将代码推送到GitHub仓库"
echo "2. 在Vercel中导入项目并部署"
echo "3. 使用PWABuilder生成APK"
echo ""
echo "🔗 有用的链接："
echo "- Vercel: https://vercel.com"
echo "- PWABuilder: https://www.pwabuilder.com"
echo "- 详细说明: 查看README.md和build-apk.md"
