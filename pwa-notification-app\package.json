{"name": "pwa-notification-app", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"@capacitor/android": "^7.4.2", "@capacitor/cli": "^7.4.2", "@capacitor/core": "^7.4.2", "pinia": "^3.0.3", "vue": "^3.5.18"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.0", "vite": "^7.0.6", "vite-plugin-pwa": "^1.0.2", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.4", "workbox-window": "^7.3.0"}}