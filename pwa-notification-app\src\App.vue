<script setup lang="ts">
import { onMounted } from 'vue'
import { useNotificationStore } from './stores/notification'
import NotificationPermission from './components/NotificationPermission.vue'
import AddNotification from './components/AddNotification.vue'
import NotificationList from './components/NotificationList.vue'

const notificationStore = useNotificationStore()

onMounted(() => {
  notificationStore.init()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 17h5l-5 5v-5zM4.868 19.718A8.966 8.966 0 0112 21a8.966 8.966 0 017.132-1.282" />
            </svg>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-gray-900">PWA推送通知</h1>
            <p class="text-sm text-gray-600">设置您的定时提醒</p>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 py-8">
      <div class="space-y-6">
        <!-- Permission Component -->
        <NotificationPermission />

        <!-- Add Notification Component -->
        <AddNotification v-if="notificationStore.isPermissionGranted" />

        <!-- Notification List Component -->
        <NotificationList v-if="notificationStore.isPermissionGranted" />

        <!-- Stats Section -->
        <div v-if="notificationStore.isPermissionGranted" class="bg-white rounded-lg shadow-md p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">统计信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 rounded-lg p-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-blue-900">总通知数</p>
                  <p class="text-2xl font-bold text-blue-600">{{ notificationStore.schedules.length }}</p>
                </div>
              </div>
            </div>

            <div class="bg-green-50 rounded-lg p-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-green-900">活跃通知</p>
                  <p class="text-2xl font-bold text-green-600">{{ notificationStore.activeSchedules.length }}</p>
                </div>
              </div>
            </div>

            <div class="bg-yellow-50 rounded-lg p-4">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-yellow-900">即将到来</p>
                  <p class="text-2xl font-bold text-yellow-600">{{ notificationStore.upcomingSchedules.length }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-12">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center text-sm text-gray-500">
          <p>PWA推送通知应用 - 支持离线使用和定时提醒</p>
          <p class="mt-1">
            <span v-if="notificationStore.isSupported" class="text-green-600">✓ 支持通知</span>
            <span v-else class="text-red-600">✗ 不支持通知</span>
            |
            <span v-if="notificationStore.isPermissionGranted" class="text-green-600">✓ 权限已授予</span>
            <span v-else class="text-yellow-600">⚠ 需要权限</span>
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped></style>
