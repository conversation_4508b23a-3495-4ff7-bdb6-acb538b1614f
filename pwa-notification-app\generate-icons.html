<!DOCTYPE html>
<html>
<head>
    <title>Generate PWA Icons</title>
</head>
<body>
    <h1>PWA Icon Generator</h1>
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <br>
    <button onclick="downloadIcon(192)">Download 192x192</button>
    <button onclick="downloadIcon(512)">Download 512x512</button>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#3B82F6';
            ctx.fillRect(0, 0, size, size);
            
            // Bell shape
            ctx.fillStyle = 'white';
            ctx.beginPath();
            const centerX = size / 2;
            const centerY = size / 2;
            const bellWidth = size * 0.3;
            const bellHeight = size * 0.4;
            
            // Draw bell
            ctx.ellipse(centerX, centerY - size * 0.05, bellWidth, bellHeight, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Bell top
            ctx.fillRect(centerX - size * 0.02, centerY - size * 0.25, size * 0.04, size * 0.1);
            
            // Notification dot
            ctx.fillStyle = '#EF4444';
            ctx.beginPath();
            ctx.arc(centerX + size * 0.15, centerY - size * 0.15, size * 0.05, 0, 2 * Math.PI);
            ctx.fill();
        }

        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `pwa-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }

        // Draw icons when page loads
        window.onload = function() {
            drawIcon(document.getElementById('canvas192'), 192);
            drawIcon(document.getElementById('canvas512'), 512);
        };
    </script>
</body>
</html>
