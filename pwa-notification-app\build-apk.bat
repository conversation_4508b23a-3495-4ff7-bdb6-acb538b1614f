@echo off
chcp 65001 >nul

echo 🚀 PWA推送通知应用 - APK构建脚本
echo.

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

echo ✅ Node.js已安装

REM 安装依赖
echo 📦 检查依赖...
if not exist "node_modules" (
    echo 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成

REM 构建项目
echo 🔨 构建PWA应用...
npm run build

if %errorlevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)

echo ✅ PWA应用构建成功

REM 检查构建输出
if not exist "dist" (
    echo ❌ 构建输出目录不存在
    pause
    exit /b 1
)

if not exist "dist\manifest.webmanifest" (
    echo ❌ PWA Manifest文件不存在
    pause
    exit /b 1
)

if not exist "dist\sw.js" (
    echo ❌ Service Worker文件不存在
    pause
    exit /b 1
)

echo ✅ PWA文件检查通过

echo.
echo 🎉 PWA应用已准备就绪！
echo.
echo 📋 接下来的步骤：
echo.
echo 方法1 - 使用PWABuilder Studio (推荐):
echo   1. 在VS Code中打开此项目
echo   2. 按 Ctrl+Shift+P 打开命令面板
echo   3. 输入 "PWABuilder: Package for App Stores"
echo   4. 选择Android平台
echo   5. 配置应用信息并生成APK
echo.
echo 方法2 - 使用PWABuilder网站:
echo   1. 先部署到Vercel或其他托管平台
echo   2. 访问 https://www.pwabuilder.com/
echo   3. 输入应用URL
echo   4. 下载生成的APK
echo.
echo 方法3 - 使用PWABuilder CLI:
echo   1. npm install -g @pwabuilder/cli
echo   2. pwa package ./dist
echo.
echo 📖 详细说明请查看:
echo   - PWABUILDER_STUDIO_GUIDE.md
echo   - build-apk.md
echo.
echo 🔍 PWA功能测试:
echo   - 在浏览器中打开 test-pwa.html
echo   - 测试所有PWA功能是否正常
echo.

pause
