{"name": "@capacitor/android", "version": "7.4.2", "description": "Capacitor: Cross-platform apps with JavaScript and the web", "homepage": "https://capacitorjs.com", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/ionic-team/capacitor.git"}, "bugs": {"url": "https://github.com/ionic-team/capacitor/issues"}, "files": ["capacitor/build.gradle", "capacitor/lint-baseline.xml", "capacitor/lint.xml", "capacitor/proguard-rules.pro", "capacitor/src/main/"], "scripts": {"verify": "./gradlew clean lint build test -b capacitor/build.gradle"}, "peerDependencies": {"@capacitor/core": "^7.4.0"}, "publishConfig": {"access": "public"}}