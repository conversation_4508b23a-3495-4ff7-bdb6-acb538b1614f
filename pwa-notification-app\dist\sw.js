if(!self.define){let e,i={};const n=(n,s)=>(n=new URL(n+".js",s).href,i[n]||new Promise(i=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=i,document.head.appendChild(e)}else e=n,importScripts(n),i()}).then(()=>{let e=i[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(s,o)=>{const r=e||("document"in self?document.currentScript.src:"")||location.href;if(i[r])return;let c={};const t=e=>n(e,r),f={module:{uri:r},exports:c,require:t};i[r]=Promise.all(s.map(e=>f[e]||t(e))).then(e=>(o(...e),c))}}define(["./workbox-74f2ef77"],function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/index-DSzFoZcW.js",revision:null},{url:"assets/index-pZhcW_by.css",revision:null},{url:"favicon.ico",revision:"1ba2ae710d927f13d483fd5d1e548c9b"},{url:"index.html",revision:"3d638d8bdf200e518cdcc4e51861ba15"},{url:"pwa-192x192.png",revision:"3f5f2aca6fc04324f3e365ad2333cf22"},{url:"pwa-512x512.png",revision:"c3fc89b36a9653061a89d6a471baab73"},{url:"pwa-icon.svg",revision:"f654d9ec144dc88ee81d95f96115585d"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"pwa-192x192.png",revision:"3f5f2aca6fc04324f3e365ad2333cf22"},{url:"pwa-512x512.png",revision:"c3fc89b36a9653061a89d6a471baab73"},{url:"manifest.webmanifest",revision:"9e1d1149c328f97222f5333b77e6e258"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/fonts\.googleapis\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-cache",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:31536e3})]}),"GET")});
