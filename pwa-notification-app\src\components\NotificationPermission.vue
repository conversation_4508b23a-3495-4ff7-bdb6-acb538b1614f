<template>
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex items-center mb-4">
      <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M15 17h5l-5 5v-5zM4.868 19.718A8.966 8.966 0 0112 21a8.966 8.966 0 017.132-1.282M6.343 6.343A8 8 0 0121.314 8.686M4.686 21.314A8 8 0 018.686 4.686" />
        </svg>
      </div>
      <div>
        <h3 class="text-lg font-semibold text-gray-900">通知权限</h3>
        <p class="text-sm text-gray-600">允许应用发送推送通知</p>
      </div>
    </div>

    <div v-if="!notificationStore.isSupported" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <div>
          <h4 class="text-sm font-medium text-red-800">不支持通知</h4>
          <p class="text-sm text-red-700 mt-1">您的浏览器不支持推送通知功能</p>
        </div>
      </div>
    </div>

    <div v-else-if="!notificationStore.isPermissionGranted" class="space-y-4">
      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <div>
            <h4 class="text-sm font-medium text-yellow-800">需要通知权限</h4>
            <p class="text-sm text-yellow-700 mt-1">请允许通知权限以接收定时提醒</p>
          </div>
        </div>
      </div>

      <button 
        @click="requestPermission"
        :disabled="isRequesting"
        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <span v-if="isRequesting">请求权限中...</span>
        <span v-else>允许通知权限</span>
      </button>
    </div>

    <div v-else class="bg-green-50 border border-green-200 rounded-md p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <div>
          <h4 class="text-sm font-medium text-green-800">权限已授予</h4>
          <p class="text-sm text-green-700 mt-1">您可以设置和接收推送通知</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useNotificationStore } from '../stores/notification'

const notificationStore = useNotificationStore()
const isRequesting = ref(false)

const requestPermission = async () => {
  isRequesting.value = true
  try {
    await notificationStore.requestPermission()
  } catch (error) {
    console.error('Failed to request notification permission:', error)
  } finally {
    isRequesting.value = false
  }
}
</script>
