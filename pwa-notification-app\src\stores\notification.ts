import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface NotificationSchedule {
  id: string
  title: string
  body: string
  scheduledTime: Date
  isActive: boolean
  repeatType: 'none' | 'daily' | 'weekly' | 'monthly'
  createdAt: Date
}

export const useNotificationStore = defineStore('notification', () => {
  // State
  const schedules = ref<NotificationSchedule[]>([])
  const isPermissionGranted = ref(false)
  const isSupported = ref(false)

  // Getters
  const activeSchedules = computed(() => 
    schedules.value.filter(schedule => schedule.isActive)
  )

  const upcomingSchedules = computed(() => 
    activeSchedules.value
      .filter(schedule => schedule.scheduledTime > new Date())
      .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime())
  )

  // Actions
  const checkNotificationSupport = () => {
    isSupported.value = 'Notification' in window && 'serviceWorker' in navigator
    return isSupported.value
  }

  const requestPermission = async () => {
    if (!isSupported.value) {
      throw new Error('Notifications are not supported in this browser')
    }

    const permission = await Notification.requestPermission()
    isPermissionGranted.value = permission === 'granted'
    return isPermissionGranted.value
  }

  const addSchedule = (schedule: Omit<NotificationSchedule, 'id' | 'createdAt'>) => {
    const newSchedule: NotificationSchedule = {
      ...schedule,
      id: crypto.randomUUID(),
      createdAt: new Date()
    }
    schedules.value.push(newSchedule)
    saveToLocalStorage()
    scheduleNotification(newSchedule)
    return newSchedule
  }

  const updateSchedule = (id: string, updates: Partial<NotificationSchedule>) => {
    const index = schedules.value.findIndex(s => s.id === id)
    if (index !== -1) {
      schedules.value[index] = { ...schedules.value[index], ...updates }
      saveToLocalStorage()
      
      // Reschedule if time or active status changed
      if (updates.scheduledTime || updates.isActive !== undefined) {
        cancelScheduledNotification(id)
        if (schedules.value[index].isActive) {
          scheduleNotification(schedules.value[index])
        }
      }
    }
  }

  const deleteSchedule = (id: string) => {
    cancelScheduledNotification(id)
    schedules.value = schedules.value.filter(s => s.id !== id)
    saveToLocalStorage()
  }

  const scheduleNotification = (schedule: NotificationSchedule) => {
    if (!isPermissionGranted.value || !schedule.isActive) return

    const now = new Date().getTime()
    const scheduledTime = schedule.scheduledTime.getTime()
    const delay = scheduledTime - now

    if (delay > 0) {
      const timeoutId = setTimeout(() => {
        showNotification(schedule)
        
        // Handle repeat notifications
        if (schedule.repeatType !== 'none') {
          scheduleNextRepeat(schedule)
        }
      }, delay)

      // Store timeout ID for cancellation
      localStorage.setItem(`notification-timeout-${schedule.id}`, timeoutId.toString())
    }
  }

  const scheduleNextRepeat = (schedule: NotificationSchedule) => {
    const nextTime = new Date(schedule.scheduledTime)
    
    switch (schedule.repeatType) {
      case 'daily':
        nextTime.setDate(nextTime.getDate() + 1)
        break
      case 'weekly':
        nextTime.setDate(nextTime.getDate() + 7)
        break
      case 'monthly':
        nextTime.setMonth(nextTime.getMonth() + 1)
        break
    }

    updateSchedule(schedule.id, { scheduledTime: nextTime })
  }

  const showNotification = (schedule: NotificationSchedule) => {
    if (!isPermissionGranted.value) return

    const notification = new Notification(schedule.title, {
      body: schedule.body,
      icon: '/pwa-192x192.png',
      badge: '/pwa-192x192.png',
      tag: schedule.id,
      requireInteraction: true
    })

    notification.onclick = () => {
      window.focus()
      notification.close()
    }
  }

  const cancelScheduledNotification = (id: string) => {
    const timeoutId = localStorage.getItem(`notification-timeout-${id}`)
    if (timeoutId) {
      clearTimeout(parseInt(timeoutId))
      localStorage.removeItem(`notification-timeout-${id}`)
    }
  }

  const saveToLocalStorage = () => {
    localStorage.setItem('notification-schedules', JSON.stringify(schedules.value))
  }

  const loadFromLocalStorage = () => {
    const saved = localStorage.getItem('notification-schedules')
    if (saved) {
      const parsed = JSON.parse(saved)
      schedules.value = parsed.map((s: any) => ({
        ...s,
        scheduledTime: new Date(s.scheduledTime),
        createdAt: new Date(s.createdAt)
      }))
      
      // Reschedule active notifications
      activeSchedules.value.forEach(schedule => {
        if (schedule.scheduledTime > new Date()) {
          scheduleNotification(schedule)
        }
      })
    }
  }

  const init = async () => {
    checkNotificationSupport()
    loadFromLocalStorage()
    
    if (isSupported.value && Notification.permission === 'granted') {
      isPermissionGranted.value = true
    }
  }

  return {
    // State
    schedules,
    isPermissionGranted,
    isSupported,
    
    // Getters
    activeSchedules,
    upcomingSchedules,
    
    // Actions
    checkNotificationSupport,
    requestPermission,
    addSchedule,
    updateSchedule,
    deleteSchedule,
    init
  }
})
