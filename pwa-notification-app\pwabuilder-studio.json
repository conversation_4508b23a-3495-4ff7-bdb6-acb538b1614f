{"manifestPath": "./dist/manifest.webmanifest", "serviceWorkerPath": "./dist/sw.js", "buildPath": "./dist", "startUrl": "/", "name": "PWA推送通知应用", "shortName": "PWA推送", "packageName": "com.pwanotification.app", "version": "1.0.0", "description": "一个支持定时推送通知的PWA应用", "themeColor": "#3B82F6", "backgroundColor": "#ffffff", "display": "standalone", "orientation": "portrait", "scope": "/", "icons": [{"src": "./dist/pwa-192x192.png", "sizes": "192x192", "type": "image/png"}, {"src": "./dist/pwa-512x512.png", "sizes": "512x512", "type": "image/png"}], "platforms": {"android": {"enabled": true, "packageId": "com.pwanotification.app", "name": "PWA推送通知应用", "version": "1.0.0", "versionCode": 1, "minSdkVersion": 21, "targetSdkVersion": 33, "permissions": ["android.permission.INTERNET", "android.permission.WAKE_LOCK", "android.permission.VIBRATE", "android.permission.POST_NOTIFICATIONS"]}, "windows": {"enabled": false}, "ios": {"enabled": false}}}