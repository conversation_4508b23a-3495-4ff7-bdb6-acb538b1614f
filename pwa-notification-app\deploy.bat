@echo off
chcp 65001 >nul

echo 🚀 开始部署PWA推送通知应用...

REM 检查Node.js和npm
echo 📋 检查环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装，请先安装Node.js
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm未安装，请先安装npm
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i

echo ✅ Node.js版本: %NODE_VERSION%
echo ✅ npm版本: %NPM_VERSION%

REM 安装依赖
echo 📦 安装依赖...
npm install

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装成功

REM 构建项目
echo 🔨 构建项目...
npm run build

if %errorlevel% neq 0 (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)

echo ✅ 项目构建成功

REM 检查构建输出
if not exist "dist" (
    echo ❌ 构建输出目录不存在
    pause
    exit /b 1
)

echo ✅ 构建输出检查通过

REM 预览构建结果
echo 👀 启动预览服务器...
echo 📝 请在浏览器中访问预览地址测试应用功能
echo 🔔 特别测试通知权限和推送功能
echo 📱 确认PWA可以正常安装

npm run preview

echo 🎉 部署脚本执行完成！
echo.
echo 📋 下一步操作：
echo 1. 将代码推送到GitHub仓库
echo 2. 在Vercel中导入项目并部署
echo 3. 使用PWABuilder生成APK
echo.
echo 🔗 有用的链接：
echo - Vercel: https://vercel.com
echo - PWABuilder: https://www.pwabuilder.com
echo - 详细说明: 查看README.md和build-apk.md

pause
