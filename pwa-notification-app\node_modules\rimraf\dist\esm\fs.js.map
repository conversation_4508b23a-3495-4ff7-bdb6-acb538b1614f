{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../../src/fs.ts"], "names": [], "mappings": "AAAA,kEAAkE;AAElE,OAAO,EAAc,MAAM,IAAI,CAAA;AAE/B,iDAAiD;AACjD,OAAO,EACL,SAAS,EACT,SAAS,EACT,UAAU,EACV,SAAS,EACT,MAAM,EACN,QAAQ,EACR,SAAS,EACT,UAAU,GACX,MAAM,IAAI,CAAA;AAEX,OAAO,EAAE,WAAW,IAAI,MAAM,EAAE,MAAM,IAAI,CAAA;AAC1C,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,IAAiB,EAAY,EAAE,CACzD,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;AAEvC,qEAAqE;AACrE,uBAAuB;AACvB,sEAAsE;AACtE,gCAAgC;AAEhC,MAAM,KAAK,GAAG,CAAC,IAAiB,EAAE,IAAa,EAAiB,EAAE,CAChE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtE,CAAA;AAEH,MAAM,KAAK,GAAG,CACZ,IAAiB,EACjB,OAIQ,EACqB,EAAE,CAC/B,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAClE,CAAA;AAEH,MAAM,OAAO,GAAG,CAAC,IAAiB,EAAqB,EAAE,CACvD,IAAI,OAAO,CAAW,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACjC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CACrD,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CACzB,CACF,CAAA;AAEH,MAAM,MAAM,GAAG,CAAC,OAAoB,EAAE,OAAoB,EAAiB,EAAE,CAC3E,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,GAAG,CAAQ,EAAE,EAAE,CAC9C,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CACzB,CACF,CAAA;AAEH,MAAM,EAAE,GAAG,CAAC,IAAiB,EAAE,OAAqB,EAAiB,EAAE,CACrE,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,GAAG,CAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtE,CAAA;AAEH,MAAM,KAAK,GAAG,CAAC,IAAiB,EAAiB,EAAE,CACjD,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChE,CAAA;AAEH,MAAM,IAAI,GAAG,CAAC,IAAiB,EAAqB,EAAE,CACpD,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CACxD,CAAA;AAEH,MAAM,KAAK,GAAG,CAAC,IAAiB,EAAqB,EAAE,CACrD,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,CAAA;AAEH,MAAM,MAAM,GAAG,CAAC,IAAiB,EAAiB,EAAE,CAClD,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CACvB,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,CAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACjE,CAAA;AAEH,MAAM,CAAC,MAAM,QAAQ,GAAG;IACtB,KAAK;IACL,KAAK;IACL,OAAO;IACP,MAAM;IACN,EAAE;IACF,KAAK;IACL,IAAI;IACJ,KAAK;IACL,MAAM;CACP,CAAA", "sourcesContent": ["// promisify ourselves, because older nodes don't have fs.promises\n\nimport fs, { Dirent } from 'fs'\n\n// sync ones just take the sync version from node\nexport {\n  chmodSync,\n  mkdirSync,\n  renameSync,\n  rmdirSync,\n  rmSync,\n  statSync,\n  lstatSync,\n  unlinkSync,\n} from 'fs'\n\nimport { readdirSync as rdSync } from 'fs'\nexport const readdirSync = (path: fs.PathLike): Dirent[] =>\n  rdSync(path, { withFileTypes: true })\n\n// unrolled for better inlining, this seems to get better performance\n// than something like:\n// const makeCb = (res, rej) => (er, ...d) => er ? rej(er) : res(...d)\n// which would be a bit cleaner.\n\nconst chmod = (path: fs.PathLike, mode: fs.Mode): Promise<void> =>\n  new Promise((res, rej) =>\n    fs.chmod(path, mode, (er, ...d: any[]) => (er ? rej(er) : res(...d))),\n  )\n\nconst mkdir = (\n  path: fs.PathLike,\n  options?:\n    | fs.Mode\n    | (fs.MakeDirectoryOptions & { recursive?: boolean | null })\n    | undefined\n    | null,\n): Promise<string | undefined> =>\n  new Promise((res, rej) =>\n    fs.mkdir(path, options, (er, made) => (er ? rej(er) : res(made))),\n  )\n\nconst readdir = (path: fs.PathLike): Promise<Dirent[]> =>\n  new Promise<Dirent[]>((res, rej) =>\n    fs.readdir(path, { withFileTypes: true }, (er, data) =>\n      er ? rej(er) : res(data),\n    ),\n  )\n\nconst rename = (oldPath: fs.PathLike, newPath: fs.PathLike): Promise<void> =>\n  new Promise((res, rej) =>\n    fs.rename(oldPath, newPath, (er, ...d: any[]) =>\n      er ? rej(er) : res(...d),\n    ),\n  )\n\nconst rm = (path: fs.PathLike, options: fs.RmOptions): Promise<void> =>\n  new Promise((res, rej) =>\n    fs.rm(path, options, (er, ...d: any[]) => (er ? rej(er) : res(...d))),\n  )\n\nconst rmdir = (path: fs.PathLike): Promise<void> =>\n  new Promise((res, rej) =>\n    fs.rmdir(path, (er, ...d: any[]) => (er ? rej(er) : res(...d))),\n  )\n\nconst stat = (path: fs.PathLike): Promise<fs.Stats> =>\n  new Promise((res, rej) =>\n    fs.stat(path, (er, data) => (er ? rej(er) : res(data))),\n  )\n\nconst lstat = (path: fs.PathLike): Promise<fs.Stats> =>\n  new Promise((res, rej) =>\n    fs.lstat(path, (er, data) => (er ? rej(er) : res(data))),\n  )\n\nconst unlink = (path: fs.PathLike): Promise<void> =>\n  new Promise((res, rej) =>\n    fs.unlink(path, (er, ...d: any[]) => (er ? rej(er) : res(...d))),\n  )\n\nexport const promises = {\n  chmod,\n  mkdir,\n  readdir,\n  rename,\n  rm,\n  rmdir,\n  stat,\n  lstat,\n  unlink,\n}\n"]}