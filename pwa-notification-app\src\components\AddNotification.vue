<template>
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">添加新通知</h3>
    
    <form @submit.prevent="addNotification" class="space-y-4">
      <div>
        <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
          标题
        </label>
        <input
          id="title"
          v-model="form.title"
          type="text"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="输入通知标题"
        />
      </div>

      <div>
        <label for="body" class="block text-sm font-medium text-gray-700 mb-1">
          内容
        </label>
        <textarea
          id="body"
          v-model="form.body"
          rows="3"
          required
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="输入通知内容"
        ></textarea>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="date" class="block text-sm font-medium text-gray-700 mb-1">
            日期
          </label>
          <input
            id="date"
            v-model="form.date"
            type="date"
            required
            :min="today"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label for="time" class="block text-sm font-medium text-gray-700 mb-1">
            时间
          </label>
          <input
            id="time"
            v-model="form.time"
            type="time"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div>
        <label for="repeat" class="block text-sm font-medium text-gray-700 mb-1">
          重复设置
        </label>
        <select
          id="repeat"
          v-model="form.repeatType"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="none">不重复</option>
          <option value="daily">每天</option>
          <option value="weekly">每周</option>
          <option value="monthly">每月</option>
        </select>
      </div>

      <div class="flex items-center">
        <input
          id="isActive"
          v-model="form.isActive"
          type="checkbox"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="isActive" class="ml-2 block text-sm text-gray-700">
          立即激活
        </label>
      </div>

      <button
        type="submit"
        :disabled="!notificationStore.isPermissionGranted || isSubmitting"
        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <span v-if="isSubmitting">添加中...</span>
        <span v-else>添加通知</span>
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useNotificationStore } from '../stores/notification'

const notificationStore = useNotificationStore()
const isSubmitting = ref(false)

const form = ref({
  title: '',
  body: '',
  date: '',
  time: '',
  repeatType: 'none' as 'none' | 'daily' | 'weekly' | 'monthly',
  isActive: true
})

const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const addNotification = async () => {
  if (!notificationStore.isPermissionGranted) {
    alert('请先允许通知权限')
    return
  }

  isSubmitting.value = true
  
  try {
    const scheduledTime = new Date(`${form.value.date}T${form.value.time}`)
    
    if (scheduledTime <= new Date()) {
      alert('请选择未来的时间')
      return
    }

    notificationStore.addSchedule({
      title: form.value.title,
      body: form.value.body,
      scheduledTime,
      repeatType: form.value.repeatType,
      isActive: form.value.isActive
    })

    // Reset form
    form.value = {
      title: '',
      body: '',
      date: '',
      time: '',
      repeatType: 'none',
      isActive: true
    }

    alert('通知添加成功！')
  } catch (error) {
    console.error('Failed to add notification:', error)
    alert('添加通知失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>
