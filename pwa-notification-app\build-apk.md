# 构建APK指南

本文档介绍如何使用PWABuilder将PWA应用打包成Android APK。

## 方法一：使用PWABuilder网站（推荐）

1. **部署应用到Vercel**
   ```bash
   # 确保代码已推送到GitHub
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   
   # 在Vercel中导入项目并部署
   ```

2. **访问PWABuilder**
   - 打开 [https://www.pwabuilder.com/](https://www.pwabuilder.com/)
   - 输入您的Vercel部署URL（例如：https://your-app.vercel.app）
   - 点击"Start"

3. **检查PWA质量**
   - PWABuilder会分析您的应用
   - 确保所有PWA要求都满足（绿色勾选）
   - 如有问题，按提示修复

4. **生成Android包**
   - 点击"Package For Stores"
   - 选择"Android"平台
   - 配置应用信息：
     - Package ID: `com.pwanotification.app`
     - App name: `PWA推送通知应用`
     - Version: `1.0.0`
   - 点击"Generate Package"

5. **下载APK**
   - 等待构建完成
   - 下载生成的APK文件
   - 可以直接安装到Android设备

## 方法二：使用PWABuilder CLI

1. **安装PWABuilder CLI**
   ```bash
   npm install -g @pwabuilder/cli
   ```

2. **生成APK**
   ```bash
   # 确保应用已部署到可访问的URL
   pwa package https://your-app.vercel.app
   
   # 或者使用本地构建
   npm run build
   pwa package ./dist
   ```

3. **配置选项**
   ```bash
   # 使用自定义配置
   pwa package https://your-app.vercel.app --platform android --package-id com.pwanotification.app
   ```

## 方法三：手动配置Android Studio

1. **下载TWA模板**
   - 从PWABuilder下载Trusted Web Activity模板
   - 或使用Google的TWA模板

2. **配置应用**
   - 修改`build.gradle`中的应用信息
   - 更新`strings.xml`中的应用名称
   - 设置正确的URL和域名验证

3. **构建APK**
   ```bash
   ./gradlew assembleRelease
   ```

## 重要配置文件

### manifest.json 要求
确保您的PWA manifest包含以下必需字段：
- `name` 和 `short_name`
- `start_url`
- `display: "standalone"`
- `icons` (至少192x192和512x512)
- `theme_color` 和 `background_color`

### Service Worker
- 必须注册Service Worker
- 支持离线功能
- 处理推送通知

### HTTPS要求
- PWA必须通过HTTPS提供服务
- Vercel自动提供HTTPS

## 测试APK

1. **安装到设备**
   ```bash
   # 使用ADB安装
   adb install app-release.apk
   ```

2. **测试功能**
   - 验证应用启动
   - 测试通知权限请求
   - 验证离线功能
   - 测试推送通知

## 发布到Google Play Store

1. **准备发布**
   - 创建签名密钥
   - 生成签名APK
   - 准备应用截图和描述

2. **上传到Play Console**
   - 创建应用列表
   - 上传APK
   - 填写应用信息
   - 提交审核

## 故障排除

### 常见问题

1. **PWA检查失败**
   - 确保manifest.json格式正确
   - 验证Service Worker注册
   - 检查HTTPS配置

2. **图标问题**
   - 确保图标尺寸正确
   - 使用PNG格式
   - 检查图标路径

3. **通知不工作**
   - 验证HTTPS环境
   - 检查权限请求
   - 确保Service Worker正确处理通知

### 调试技巧

1. **使用Chrome DevTools**
   ```
   F12 -> Application -> Manifest
   F12 -> Application -> Service Workers
   ```

2. **测试PWA功能**
   ```
   Chrome -> 设置 -> 更多工具 -> 开发者工具 -> Lighthouse
   ```

3. **验证通知**
   ```javascript
   // 在控制台测试
   new Notification('Test', { body: 'Testing notifications' });
   ```

## 自动化构建

可以创建GitHub Actions来自动构建和部署：

```yaml
name: Build and Deploy PWA
on:
  push:
    branches: [ main ]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm install
      - name: Build
        run: npm run build
      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
```

## 更多资源

- [PWABuilder文档](https://docs.pwabuilder.com/)
- [Google PWA指南](https://web.dev/progressive-web-apps/)
- [Android TWA文档](https://developer.chrome.com/docs/android/trusted-web-activity/)
- [Vercel部署指南](https://vercel.com/docs)
