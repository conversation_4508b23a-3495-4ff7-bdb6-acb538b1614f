# PWA推送通知应用

一个基于Vue 3 + Vite + Tailwind CSS + Workbox + Pinia构建的PWA应用，支持定时推送通知功能。

## 功能特性

- 🔔 **本地推送通知** - 支持设置定时推送提醒
- 📱 **PWA支持** - 可安装到桌面，支持离线使用
- 🔄 **重复提醒** - 支持每天、每周、每月重复提醒
- 💾 **本地存储** - 数据保存在本地，无需服务器
- 🎨 **响应式设计** - 适配各种设备屏幕
- ⚡ **快速加载** - 使用Vite构建，加载速度快

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Pinia
- **PWA工具**: Workbox (vite-plugin-pwa)
- **部署平台**: Vercel

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 部署到Vercel

1. 将代码推送到GitHub仓库
2. 在Vercel中导入项目
3. Vercel会自动检测到这是一个Vite项目并进行构建
4. 部署完成后即可访问PWA应用

### 手动部署

```bash
# 安装Vercel CLI
npm i -g vercel

# 登录Vercel
vercel login

# 部署
vercel --prod
```

## 使用PWABuilder打包APK

1. 访问 [PWABuilder](https://www.pwabuilder.com/)
2. 输入您的PWA应用URL
3. 选择Android平台
4. 下载生成的APK文件

### 或者使用命令行

```bash
# 安装PWABuilder CLI
npm install -g @pwabuilder/cli

# 生成APK
pwa package https://your-app-url.vercel.app
```

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── NotificationPermission.vue  # 权限请求组件
│   ├── AddNotification.vue         # 添加通知组件
│   └── NotificationList.vue        # 通知列表组件
├── stores/             # Pinia状态管理
│   └── notification.ts # 通知相关状态
├── App.vue            # 主应用组件
├── main.ts           # 应用入口
└── style.css         # 全局样式
```

## 浏览器支持

- Chrome 42+
- Firefox 44+
- Safari 16+
- Edge 17+

## 注意事项

1. **HTTPS要求**: 推送通知需要在HTTPS环境下工作
2. **权限请求**: 首次使用需要用户授权通知权限
3. **浏览器限制**: 不同浏览器对通知的支持可能有差异
4. **离线功能**: Service Worker会缓存应用资源，支持离线使用
