# PWABuilder Studio 使用指南

## 🚀 使用PWABuilder Studio打包APK

### 前提条件
- ✅ VS Code已安装PWABuilder Studio扩展
- ✅ 项目已成功构建 (`npm run build`)
- ✅ Android SDK已安装（可选，用于本地构建）

### 步骤1：准备项目

1. **确保项目已构建**
   ```bash
   npm run build
   ```
   
2. **检查构建输出**
   - 确认 `dist/` 目录存在
   - 确认 `dist/manifest.webmanifest` 文件存在
   - 确认 `dist/sw.js` Service Worker文件存在

### 步骤2：使用PWABuilder Studio

1. **打开命令面板**
   - 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (Mac)

2. **运行PWABuilder命令**
   - 输入 "PWABuilder"
   - 选择 "PWABuilder: Package for App Stores"

3. **配置应用信息**
   - **App Name**: PWA推送通知应用
   - **Package Name**: com.pwanotification.app
   - **Version**: 1.0.0
   - **Build Path**: ./dist

4. **选择平台**
   - ✅ Android (生成APK)
   - ⚪ Windows (可选)
   - ⚪ iOS (需要Mac)

### 步骤3：Android配置

1. **基本设置**
   - **Target SDK**: 33 (Android 13)
   - **Min SDK**: 21 (Android 5.0)
   - **Theme Color**: #3B82F6
   - **Background Color**: #ffffff

2. **权限设置**
   - ✅ INTERNET
   - ✅ WAKE_LOCK
   - ✅ VIBRATE
   - ✅ POST_NOTIFICATIONS (Android 13+)

3. **功能设置**
   - ✅ Enable Notifications
   - ✅ Enable Fullscreen
   - ✅ Enable Orientation Lock

### 步骤4：生成APK

1. **开始构建**
   - 点击 "Generate Package"
   - 等待构建完成

2. **下载APK**
   - 构建完成后会显示下载链接
   - APK文件通常保存在项目的 `platforms/android/` 目录

### 步骤5：测试APK

1. **安装到设备**
   ```bash
   # 使用ADB安装
   adb install app-release.apk
   
   # 或者直接传输到设备安装
   ```

2. **测试功能**
   - ✅ 应用启动
   - ✅ 通知权限请求
   - ✅ 添加通知
   - ✅ 接收通知
   - ✅ 离线功能

## 🛠️ 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理并重新构建
   rm -rf dist node_modules
   npm install
   npm run build
   ```

2. **Manifest错误**
   - 检查 `dist/manifest.webmanifest` 是否存在
   - 确认所有必需字段都已填写

3. **图标问题**
   - 确保 `dist/pwa-192x192.png` 和 `dist/pwa-512x512.png` 存在
   - 图标必须是PNG格式

4. **Service Worker问题**
   - 确认 `dist/sw.js` 文件存在
   - 检查控制台是否有Service Worker错误

### 调试技巧

1. **使用Chrome DevTools**
   ```
   F12 -> Application -> Manifest
   F12 -> Application -> Service Workers
   ```

2. **PWA检查**
   ```
   F12 -> Lighthouse -> Progressive Web App
   ```

3. **Android调试**
   ```bash
   # 连接设备并查看日志
   adb logcat | grep -i "pwa\|notification"
   ```

## 📱 高级配置

### 自定义启动画面

在 `dist/manifest.webmanifest` 中添加：
```json
{
  "splash_pages": null,
  "theme_color": "#3B82F6",
  "background_color": "#ffffff"
}
```

### 自定义图标

替换以下文件：
- `dist/pwa-192x192.png`
- `dist/pwa-512x512.png`
- `dist/favicon.ico`

### 签名APK

1. **生成密钥库**
   ```bash
   keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
   ```

2. **在PWABuilder Studio中配置签名**
   - Keystore Path: 密钥库文件路径
   - Alias: my-key-alias
   - Password: 您设置的密码

## 🚀 发布到Google Play

1. **准备发布**
   - 生成签名APK
   - 准备应用截图
   - 编写应用描述

2. **上传到Play Console**
   - 创建应用列表
   - 上传APK
   - 填写应用信息
   - 提交审核

## 📋 检查清单

在打包APK之前，请确认：

- [ ] 项目成功构建 (`npm run build`)
- [ ] PWA功能正常工作
- [ ] 通知权限正常请求
- [ ] 离线功能正常
- [ ] 图标文件存在且格式正确
- [ ] Manifest文件完整
- [ ] Service Worker注册成功

## 🔗 相关资源

- [PWABuilder Studio文档](https://docs.pwabuilder.com/studio/)
- [Android开发者指南](https://developer.android.com/)
- [PWA最佳实践](https://web.dev/progressive-web-apps/)

---

**💡 提示**: 如果遇到问题，可以查看VS Code的输出面板中的PWABuilder日志获取详细错误信息。
