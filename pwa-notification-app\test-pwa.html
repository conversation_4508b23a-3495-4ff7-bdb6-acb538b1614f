<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f0f8f0; }
        .error { border-color: #f44336; background-color: #fdf0f0; }
        .warning { border-color: #ff9800; background-color: #fff8f0; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .status { font-weight: bold; margin-left: 10px; }
    </style>
</head>
<body>
    <h1>PWA推送通知应用 - 功能测试</h1>
    
    <div class="test-item" id="https-test">
        <h3>1. HTTPS检查</h3>
        <p>PWA需要在HTTPS环境下运行</p>
        <span class="status" id="https-status">检查中...</span>
    </div>

    <div class="test-item" id="manifest-test">
        <h3>2. Manifest检查</h3>
        <p>检查Web App Manifest是否正确配置</p>
        <button onclick="checkManifest()">检查Manifest</button>
        <span class="status" id="manifest-status"></span>
    </div>

    <div class="test-item" id="sw-test">
        <h3>3. Service Worker检查</h3>
        <p>检查Service Worker是否注册成功</p>
        <button onclick="checkServiceWorker()">检查Service Worker</button>
        <span class="status" id="sw-status"></span>
    </div>

    <div class="test-item" id="notification-test">
        <h3>4. 通知权限测试</h3>
        <p>测试浏览器通知功能</p>
        <button onclick="testNotification()">测试通知</button>
        <span class="status" id="notification-status"></span>
    </div>

    <div class="test-item" id="install-test">
        <h3>5. PWA安装测试</h3>
        <p>测试应用是否可以安装到设备</p>
        <button onclick="testInstall()">测试安装</button>
        <span class="status" id="install-status"></span>
    </div>

    <div class="test-item" id="offline-test">
        <h3>6. 离线功能测试</h3>
        <p>测试应用离线工作能力</p>
        <button onclick="testOffline()">测试离线</button>
        <span class="status" id="offline-status"></span>
    </div>

    <script>
        // 检查HTTPS
        function checkHTTPS() {
            const httpsTest = document.getElementById('https-test');
            const httpsStatus = document.getElementById('https-status');
            
            if (location.protocol === 'https:' || location.hostname === 'localhost') {
                httpsTest.className = 'test-item success';
                httpsStatus.textContent = '✅ HTTPS环境正常';
            } else {
                httpsTest.className = 'test-item error';
                httpsStatus.textContent = '❌ 需要HTTPS环境';
            }
        }

        // 检查Manifest
        function checkManifest() {
            const manifestStatus = document.getElementById('manifest-status');
            const manifestTest = document.getElementById('manifest-test');
            
            fetch('/manifest.webmanifest')
                .then(response => response.json())
                .then(manifest => {
                    if (manifest.name && manifest.start_url && manifest.icons) {
                        manifestTest.className = 'test-item success';
                        manifestStatus.textContent = '✅ Manifest配置正确';
                    } else {
                        manifestTest.className = 'test-item warning';
                        manifestStatus.textContent = '⚠️ Manifest配置不完整';
                    }
                })
                .catch(error => {
                    manifestTest.className = 'test-item error';
                    manifestStatus.textContent = '❌ Manifest加载失败';
                });
        }

        // 检查Service Worker
        function checkServiceWorker() {
            const swStatus = document.getElementById('sw-status');
            const swTest = document.getElementById('sw-test');
            
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations()
                    .then(registrations => {
                        if (registrations.length > 0) {
                            swTest.className = 'test-item success';
                            swStatus.textContent = '✅ Service Worker已注册';
                        } else {
                            swTest.className = 'test-item warning';
                            swStatus.textContent = '⚠️ Service Worker未注册';
                        }
                    });
            } else {
                swTest.className = 'test-item error';
                swStatus.textContent = '❌ 浏览器不支持Service Worker';
            }
        }

        // 测试通知
        function testNotification() {
            const notificationStatus = document.getElementById('notification-status');
            const notificationTest = document.getElementById('notification-test');
            
            if (!('Notification' in window)) {
                notificationTest.className = 'test-item error';
                notificationStatus.textContent = '❌ 浏览器不支持通知';
                return;
            }

            if (Notification.permission === 'granted') {
                new Notification('测试通知', {
                    body: '通知功能正常工作！',
                    icon: '/pwa-192x192.png'
                });
                notificationTest.className = 'test-item success';
                notificationStatus.textContent = '✅ 通知权限已授予';
            } else if (Notification.permission === 'denied') {
                notificationTest.className = 'test-item error';
                notificationStatus.textContent = '❌ 通知权限被拒绝';
            } else {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('测试通知', {
                            body: '通知功能正常工作！',
                            icon: '/pwa-192x192.png'
                        });
                        notificationTest.className = 'test-item success';
                        notificationStatus.textContent = '✅ 通知权限已授予';
                    } else {
                        notificationTest.className = 'test-item error';
                        notificationStatus.textContent = '❌ 通知权限被拒绝';
                    }
                });
            }
        }

        // 测试安装
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            const installTest = document.getElementById('install-test');
            const installStatus = document.getElementById('install-status');
            installTest.className = 'test-item success';
            installStatus.textContent = '✅ 应用可以安装';
        });

        function testInstall() {
            const installStatus = document.getElementById('install-status');
            const installTest = document.getElementById('install-test');
            
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        installStatus.textContent = '✅ 用户接受安装';
                    } else {
                        installStatus.textContent = '⚠️ 用户取消安装';
                    }
                    deferredPrompt = null;
                });
            } else {
                installTest.className = 'test-item warning';
                installStatus.textContent = '⚠️ 应用已安装或不可安装';
            }
        }

        // 测试离线
        function testOffline() {
            const offlineStatus = document.getElementById('offline-status');
            const offlineTest = document.getElementById('offline-test');
            
            if (navigator.onLine) {
                offlineTest.className = 'test-item success';
                offlineStatus.textContent = '✅ 当前在线，请断网测试离线功能';
            } else {
                offlineTest.className = 'test-item success';
                offlineStatus.textContent = '✅ 当前离线，应用仍可访问';
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            checkHTTPS();
            checkManifest();
            checkServiceWorker();
        });

        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('网络已连接');
        });

        window.addEventListener('offline', () => {
            console.log('网络已断开');
        });
    </script>
</body>
</html>
