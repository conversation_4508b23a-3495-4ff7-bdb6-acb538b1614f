{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "pwabuilder.manifestPath": "./dist/manifest.webmanifest", "pwabuilder.buildPath": "./dist", "pwabuilder.packageName": "com.pwanotification.app", "pwabuilder.appName": "PWA推送通知应用", "pwabuilder.appVersion": "1.0.0", "pwabuilder.enableAndroid": true, "pwabuilder.enableWindows": true, "pwabuilder.enableIOS": false}